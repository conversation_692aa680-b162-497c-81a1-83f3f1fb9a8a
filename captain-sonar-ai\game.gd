extends Control


func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://main_menu.tscn")
	
const MAP_WIDTH = 15
const MAP_HEIGHT = 15

@onready var tile_map_layer = $TileMap/TileMapLayer

const TILE_ATLAS_COORD = Vector2i(0, 0)

const WATER_SOURCE_ID = 0
const ISLAND_SOURCE_ID = 1

var map_data = []


func _ready():
	_generate_map_data()
		_render_map_from_data()
			if engine.has_singleton("GameState"):
				GameState.initialize_game(map_data)

# Fills map_data array with true (island) or false (water).
# Currently makes a boarder around
func _generate_map_data():
	for x in MAP_WIDTH:
		map_data.append([]) # Add a new column
		for y in MAP_HEIGHT:
			map_data[x].append(false) # Fill with water by default


	for x in MAP_WIDTH:
		for y in MAP_HEIGHT:
			# If the tile is on the edge of the map, make it an island
			if x == 0 or x == MAP_WIDTH - 1 or y == 0 or y == MAP_HEIGHT - 1:
				map_data[x][y] = true

# This function reads map_data and places the correct tiles.
func _render_map_from_data():
	tile_map_layer.clear()

	for x in MAP_WIDTH:
		for y in MAP_HEIGHT:
			if map_data[x][y] == true:
				# It's an island! Use the ISLAND source and the TILE atlas coord.
				tile_map_layer.set_cell(Vector2i(x, y), ISLAND_SOURCE_ID, TILE_ATLAS_COORD)
			else:
				# It's water. Use the WATER source and the TILE atlas coord.
				tile_map_layer.set_cell(Vector2i(x, y), WATER_SOURCE_ID, TILE_ATLAS_COORD)
