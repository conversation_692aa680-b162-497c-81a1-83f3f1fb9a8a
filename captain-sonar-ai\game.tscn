[gd_scene load_steps=9 format=3 uid="uid://ucgc5ulngwmb"]

[ext_resource type="Script" uid="uid://df1mmeywv0hc6" path="res://game.gd" id="1_80nbo"]
[ext_resource type="Texture2D" uid="uid://duinn1jaa03f7" path="res://water_icon 16x.svg" id="2_7jktm"]
[ext_resource type="Texture2D" uid="uid://b76ed6gomomec" path="res://island_icon 16x.svg" id="3_ryrav"]
[ext_resource type="PackedScene" uid="uid://dmjs78lirhs7n" path="res://player.tscn" id="4_eow3j"]
[ext_resource type="PackedScene" uid="uid://du6v8cr11f81i" path="res://EnemyAI.tscn" id="5_j5wjh"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_eow3j"]
texture = ExtResource("2_7jktm")
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_j5wjh"]
texture = ExtResource("3_ryrav")
0:0/0 = 0

[sub_resource type="TileSet" id="TileSet_mfdv2"]
custom_data_layer_0/name = "is_land"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_eow3j")
sources/1 = SubResource("TileSetAtlasSource_j5wjh")

[node name="CaptainStation" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_80nbo")

[node name="Label" type="Label" parent="."]
layout_mode = 0
offset_left = 488.0
offset_top = 268.0
offset_right = 677.0
offset_bottom = 291.0
text = "This is the Game Screen!"

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 5.0
offset_top = 5.0
offset_right = 105.0
offset_bottom = 36.0
text = "Back"

[node name="TileMap" type="TileMap" parent="."]
position = Vector2(300, 300)
tile_set = SubResource("TileSet_mfdv2")
format = 2

[node name="TileMapLayer" type="TileMapLayer" parent="TileMap"]
tile_set = SubResource("TileSet_mfdv2")

[node name="Player" parent="." instance=ExtResource("4_eow3j")]
position = Vector2(216, 176)

[node name="Player2" parent="." instance=ExtResource("5_j5wjh")]
position = Vector2(733, 136)

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
