[gd_scene load_steps=9 format=3 uid="uid://ucgc5ulngwmb"]

[ext_resource type="Script" uid="uid://df1mmeywv0hc6" path="res://game.gd" id="1_80nbo"]
[ext_resource type="Texture2D" uid="uid://duinn1jaa03f7" path="res://water_icon 16x.svg" id="2_7jktm"]
[ext_resource type="Texture2D" uid="uid://b76ed6gomomec" path="res://island_icon 16x.svg" id="3_ryrav"]
[ext_resource type="Texture2D" uid="uid://dmfkpv1eqvqyk" path="res://player_icon.svg" id="4_player"]
[ext_resource type="Texture2D" uid="uid://xj62mg58llei" path="res://enemy_sub_icon.svg" id="5_enemy"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_eow3j"]
texture = ExtResource("2_7jktm")
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_j5wjh"]
texture = ExtResource("3_ryrav")
0:0/0 = 0

[sub_resource type="TileSet" id="TileSet_mfdv2"]
custom_data_layer_0/name = "is_land"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_eow3j")
sources/1 = SubResource("TileSetAtlasSource_j5wjh")

[node name="CaptainStation" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_80nbo")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 10.0
offset_top = 10.0
offset_right = 80.0
offset_bottom = 40.0
text = "Back"

[node name="StatusLabel" type="Label" parent="."]
layout_mode = 0
offset_left = 100.0
offset_top = 10.0
offset_right = 600.0
offset_bottom = 40.0
text = "Captain Station - Ready for Orders"

[node name="TileMap" type="TileMap" parent="."]
position = Vector2(50, 80)
tile_set = SubResource("TileSet_mfdv2")
format = 2

[node name="TileMapLayer" type="TileMapLayer" parent="TileMap"]
tile_set = SubResource("TileSet_mfdv2")

[node name="PlayerSubmarine" type="Sprite2D" parent="."]
position = Vector2(114, 144)
scale = Vector2(0.5, 0.5)
texture = ExtResource("4_player")

[node name="EnemySubmarine" type="Sprite2D" parent="."]
visible = false
position = Vector2(500, 300)
scale = Vector2(0.5, 0.5)
texture = ExtResource("5_enemy")

[node name="MovementPanel" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 1000.0
offset_top = 100.0
offset_right = 1150.0
offset_bottom = 300.0

[node name="MovementLabel" type="Label" parent="MovementPanel"]
layout_mode = 2
text = "Movement Controls"
horizontal_alignment = 1

[node name="NorthButton" type="Button" parent="MovementPanel"]
layout_mode = 2
text = "North"

[node name="HBoxContainer" type="HBoxContainer" parent="MovementPanel"]
layout_mode = 2

[node name="WestButton" type="Button" parent="MovementPanel/HBoxContainer"]
layout_mode = 2
text = "West"

[node name="EastButton" type="Button" parent="MovementPanel/HBoxContainer"]
layout_mode = 2
text = "East"

[node name="SouthButton" type="Button" parent="MovementPanel"]
layout_mode = 2
text = "South"

[node name="TestPanel" type="VBoxContainer" parent="."]
layout_mode = 0
offset_left = 1000.0
offset_top = 350.0
offset_right = 1150.0
offset_bottom = 450.0

[node name="TestLabel" type="Label" parent="TestPanel"]
layout_mode = 2
text = "Test Controls"
horizontal_alignment = 1

[node name="SurfaceEnemyButton" type="Button" parent="TestPanel"]
layout_mode = 2
text = "Surface Enemy"

[node name="SubmergeEnemyButton" type="Button" parent="TestPanel"]
layout_mode = 2
text = "Submerge Enemy"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="MovementPanel/NorthButton" to="." method="_move_north"]
[connection signal="pressed" from="MovementPanel/HBoxContainer/WestButton" to="." method="_move_west"]
[connection signal="pressed" from="MovementPanel/HBoxContainer/EastButton" to="." method="_move_east"]
[connection signal="pressed" from="MovementPanel/SouthButton" to="." method="_move_south"]
[connection signal="pressed" from="TestPanel/SurfaceEnemyButton" to="." method="_surface_enemy"]
[connection signal="pressed" from="TestPanel/SubmergeEnemyButton" to="." method="_submerge_enemy"]
