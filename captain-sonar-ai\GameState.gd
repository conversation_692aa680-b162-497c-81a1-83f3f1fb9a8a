extends Node

signal submarine_moved(team_id, direction)
signal state_updated(team_id)

const TEAM_IDS = ["alpha", "bravo"]

var teams: Dictionary = {}

# Initializes a fresh match. This should be called at the start of a game.
func initialize_game(map_data: Array) -> void:
	teams.clear()
	for id in TEAM_IDS:
		teams[id] = SubmarineState.new()

	# Set starting positions for submarines
	if teams.has("alpha"):
		teams["alpha"].position = Vector2i(2, 2)
		teams["alpha"].heading = "EAST"

	if teams.has("bravo"):
		teams["bravo"].position = Vector2i(12, 12)
		teams["bravo"].heading = "WEST"
		teams["bravo"].is_surfaced = false  # Enemy starts submerged

	# Additional map_data could be stored here if needed.
	for team_id in TEAM_IDS:
		emit_signal("state_updated", team_id)

# Updates a submarine position and logs the move.
func process_captain_move(team_id: String, direction: String) -> void:
	if not teams.has(team_id):
		return
	var state: SubmarineState = teams[team_id]
	var delta := Vector2i.ZERO
	match direction:
		"NORTH":
			delta = Vector2i(0, -1)
		"SOUTH":
			delta = Vector2i(0, 1)
		"EAST":
			delta = Vector2i(1, 0)
		"WEST":
			delta = Vector2i(-1, 0)
		_:
			return
	state.position += delta
	state.heading = direction
	state.move_log.append(direction)
	state.heat += 0.1
	emit_signal("submarine_moved", team_id, direction)
	emit_signal("state_updated", team_id)
