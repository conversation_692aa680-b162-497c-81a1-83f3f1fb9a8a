extends Node

signal submarine_moved(team_id, direction)
signal state_updated(team_id)

const TEAM_IDS = ["alpha", "bravo"]

# Player role and AI difficulty selections
enum PlayerRole {
	NONE,
	RADIO_OPERATOR,
	CAPTAIN,
	ENGINEER,
	FIRST_MATE
}

enum AIDifficulty {
	NONE,
	EASY,
	MEDIUM,
	HARD
}

var teams: Dictionary = {}
var player_role: PlayerRole = PlayerRole.NONE
var ai_difficulty: AIDifficulty = AIDifficulty.NONE
var map_data: Array = []  # Store map data for collision detection

# Initializes a fresh match. This should be called at the start of a game.
func initialize_game(map_data_input: Array) -> void:
	teams.clear()
	map_data = map_data_input  # Store map data for collision detection

	for id in TEAM_IDS:
		teams[id] = SubmarineState.new()

	# Set starting positions for submarines (in valid water areas, avoiding border islands)
	if teams.has("alpha"):
		teams["alpha"].position = Vector2i(2, 2)  # Safe water position
		teams["alpha"].heading = "EAST"

	if teams.has("bravo"):
		teams["bravo"].position = Vector2i(12, 12)  # Safe water position
		teams["bravo"].heading = "WEST"
		teams["bravo"].is_surfaced = false  # Enemy starts submerged

	# Emit initial state updates
	for team_id in TEAM_IDS:
		emit_signal("state_updated", team_id)

# Updates a submarine position and logs the move.
func process_captain_move(team_id: String, direction: String) -> void:
	if not teams.has(team_id):
		return
	var state: SubmarineState = teams[team_id]
	var delta := Vector2i.ZERO
	match direction:
		"NORTH":
			delta = Vector2i(0, -1)
		"SOUTH":
			delta = Vector2i(0, 1)
		"EAST":
			delta = Vector2i(1, 0)
		"WEST":
			delta = Vector2i(-1, 0)
		_:
			return

	# Calculate new position
	var new_position = state.position + delta

	# Check boundaries (15x15 map, valid positions from 0 to 14)
	if new_position.x < 0 or new_position.x >= 15 or new_position.y < 0 or new_position.y >= 15:
		print("Cannot move %s - would go outside map boundaries!" % direction)
		return

	# Check for collision with islands (if map_data is available)
	if map_data.size() > 0 and new_position.x < map_data.size() and new_position.y < map_data[new_position.x].size():
		if map_data[new_position.x][new_position.y] == true:  # true = island
			print("Cannot move %s - would hit an island!" % direction)
			return

	# Update position only if valid
	state.position = new_position
	state.heading = direction
	state.move_log.append(direction)
	state.heat += 0.1
	emit_signal("submarine_moved", team_id, direction)
	emit_signal("state_updated", team_id)
