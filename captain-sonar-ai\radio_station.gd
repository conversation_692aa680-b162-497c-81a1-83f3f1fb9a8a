extends Control

# Get a reference to the PathOverlay instance in your scene.
@onready var path_overlay = $PathOverlay 
# (Adjust path if needed, e.g., $MainLayout/PathOverlay)

# This would be connected to a signal from your game's logic
# when the enemy sub makes a move.
func _on_enemy_moved(direction: String):
	# This is a placeholder for the real enemy path.
	var current_enemy_path = path_overlay.move_path
	current_enemy_path.append(direction)
	
	# Update the UI
	path_overlay.set_new_path(current_enemy_path)
	_update_move_log_ui() # Your function to update the VBoxContainer with labels


# This would be called when the enemy surfaces.
func _on_enemy_surfaced():
	path_overlay.clear_path()
	# Clear your move log UI as well.
