[res://MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://game.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 5,
"row": 21,
"scroll_position": 4.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://settings.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://help.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 56,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://AIProfile.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 48,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 10,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://DraggableOverlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 11,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://MoveEntry.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 0,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://path_overlay.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 20,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 4,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://radio_station.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 16,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://SpectatorView.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 35,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 19,
"scroll_position": 10.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://GameState.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 6,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://PlayerSelection.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 128,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 116,
"scroll_position": 106.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
