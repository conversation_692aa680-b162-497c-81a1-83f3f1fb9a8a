extends Control

# Player Selection Screen
# Allows player to choose their role and AI difficulty

@onready var role_buttons = $CenterContainer/VBoxContainer/RoleSelection/RoleButtons
@onready var difficulty_buttons = $CenterContainer/VBoxContainer/DifficultySelection/DifficultyButtons
@onready var start_game_button = $CenterContainer/VBoxContainer/StartGameButton
@onready var back_button = $BackButton
@onready var selected_role_label = $CenterContainer/VBoxContainer/SelectedRole
@onready var selected_difficulty_label = $CenterContainer/VBoxContainer/SelectedDifficulty



var selected_role: GameState.PlayerRole = GameState.PlayerRole.NONE
var selected_difficulty: GameState.AIDifficulty = GameState.AIDifficulty.NONE

func _ready() -> void:
	_setup_role_buttons()
	_setup_difficulty_buttons()
	_update_ui()

func _setup_role_buttons() -> void:
	# Connect role button signals
	var radio_button = role_buttons.get_node("RadioOperatorButton")
	var captain_button = role_buttons.get_node("CaptainButton")
	var engineer_button = role_buttons.get_node("EngineerButton")
	var first_mate_button = role_buttons.get_node("FirstMateButton")
	
	radio_button.connect("pressed", _on_role_selected.bind(GameState.PlayerRole.RADIO_OPERATOR))
	captain_button.connect("pressed", _on_role_selected.bind(GameState.PlayerRole.CAPTAIN))
	engineer_button.connect("pressed", _on_role_selected.bind(GameState.PlayerRole.ENGINEER))
	first_mate_button.connect("pressed", _on_role_selected.bind(GameState.PlayerRole.FIRST_MATE))

func _setup_difficulty_buttons() -> void:
	# Connect difficulty button signals
	var easy_button = difficulty_buttons.get_node("EasyButton")
	var medium_button = difficulty_buttons.get_node("MediumButton")
	var hard_button = difficulty_buttons.get_node("HardButton")
	
	easy_button.connect("pressed", _on_difficulty_selected.bind(GameState.AIDifficulty.EASY))
	medium_button.connect("pressed", _on_difficulty_selected.bind(GameState.AIDifficulty.MEDIUM))
	hard_button.connect("pressed", _on_difficulty_selected.bind(GameState.AIDifficulty.HARD))

func _on_role_selected(role: GameState.PlayerRole) -> void:
	selected_role = role
	_update_role_button_states()
	_update_ui()

func _on_difficulty_selected(difficulty: GameState.AIDifficulty) -> void:
	selected_difficulty = difficulty
	_update_difficulty_button_states()
	_update_ui()

func _update_role_button_states() -> void:
	# Reset all role buttons
	for button in role_buttons.get_children():
		button.button_pressed = false
	
	# Set selected button as pressed
	match selected_role:
		GameState.PlayerRole.RADIO_OPERATOR:
			role_buttons.get_node("RadioOperatorButton").button_pressed = true
		GameState.PlayerRole.CAPTAIN:
			role_buttons.get_node("CaptainButton").button_pressed = true
		GameState.PlayerRole.ENGINEER:
			role_buttons.get_node("EngineerButton").button_pressed = true
		GameState.PlayerRole.FIRST_MATE:
			role_buttons.get_node("FirstMateButton").button_pressed = true

func _update_difficulty_button_states() -> void:
	# Reset all difficulty buttons
	for button in difficulty_buttons.get_children():
		button.button_pressed = false
	
	# Set selected button as pressed
	match selected_difficulty:
		GameState.AIDifficulty.EASY:
			difficulty_buttons.get_node("EasyButton").button_pressed = true
		GameState.AIDifficulty.MEDIUM:
			difficulty_buttons.get_node("MediumButton").button_pressed = true
		GameState.AIDifficulty.HARD:
			difficulty_buttons.get_node("HardButton").button_pressed = true

func _update_ui() -> void:
	# Update selected role label
	var role_text = "Selected Role: "
	match selected_role:
		GameState.PlayerRole.RADIO_OPERATOR:
			role_text += "Radio Operator"
		GameState.PlayerRole.CAPTAIN:
			role_text += "Captain"
		GameState.PlayerRole.ENGINEER:
			role_text += "Engineer"
		GameState.PlayerRole.FIRST_MATE:
			role_text += "First Mate"
		_:
			role_text += "None"
	
	selected_role_label.text = role_text
	
	# Update selected difficulty label
	var difficulty_text = "AI Difficulty: "
	match selected_difficulty:
		GameState.AIDifficulty.EASY:
			difficulty_text += "Easy"
		GameState.AIDifficulty.MEDIUM:
			difficulty_text += "Medium"
		GameState.AIDifficulty.HARD:
			difficulty_text += "Hard"
		_:
			difficulty_text += "None"
	
	selected_difficulty_label.text = difficulty_text
	
	# Enable start button only if both role and difficulty are selected
	start_game_button.disabled = (selected_role == GameState.PlayerRole.NONE or selected_difficulty == GameState.AIDifficulty.NONE)

func _on_start_game_button_pressed() -> void:
	if selected_role == GameState.PlayerRole.NONE or selected_difficulty == GameState.AIDifficulty.NONE:
		return
	
	# Store selections in GameState for other scenes to access
	if GameState:
		GameState.player_role = selected_role
		GameState.ai_difficulty = selected_difficulty
	
	# Navigate to appropriate scene based on selected role
	match selected_role:
		GameState.PlayerRole.RADIO_OPERATOR:
			get_tree().change_scene_to_file("res://radio_station.tscn")
		GameState.PlayerRole.CAPTAIN:
			get_tree().change_scene_to_file("res://game.tscn")
		GameState.PlayerRole.ENGINEER:
			get_tree().change_scene_to_file("res://engineer_station.tscn")
		GameState.PlayerRole.FIRST_MATE:
			get_tree().change_scene_to_file("res://weapons_station.tscn")

func _on_back_button_pressed() -> void:
	get_tree().change_scene_to_file("res://main_menu.tscn")
