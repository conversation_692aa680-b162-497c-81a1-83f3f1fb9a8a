[gd_scene load_steps=2 format=3 uid="uid://b1hpdws5k06wh"]

[ext_resource type="Script" uid="uid://d0sys4x07ive3" path="res://PlayerSelection.gd" id="1_player_sel"]

[node name="PlayerSelection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_player_sel")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="ScrollContainer" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = 70.0
offset_right = -50.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="TitleLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Captain Sonar - Crew Assignment"
horizontal_alignment = 1

[node name="InstructionLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Assign crew members to each station on both submarines. Choose 'Human' to control a station yourself, or select AI difficulty."
horizontal_alignment = 1
autowrap_mode = 3

[node name="HSeparator2" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="PlayerSubPanel" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="PlayerSubLabel" type="Label" parent="ScrollContainer/VBoxContainer/PlayerSubPanel"]
layout_mode = 2
text = "Your Submarine (Alpha Team)"
horizontal_alignment = 1

[node name="HSeparator3" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="EnemySubPanel" type="VBoxContainer" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="EnemySubLabel" type="Label" parent="ScrollContainer/VBoxContainer/EnemySubPanel"]
layout_mode = 2
text = "Enemy Submarine (Bravo Team)"
horizontal_alignment = 1

[node name="HSeparator4" type="HSeparator" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2

[node name="StatusLabel" type="Label" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
text = "Select at least one Human station to continue"
horizontal_alignment = 1

[node name="StartGameButton" type="Button" parent="ScrollContainer/VBoxContainer"]
layout_mode = 2
disabled = true
text = "Start Game"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation#RadioLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Radio Operator:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation#RadioOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation_RadioOptions#RadioHuman" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station - leaves station interface empty for manual control"
toggle_mode = true
text = "Human"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation_RadioOptions#RadioEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation_RadioOptions#RadioNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_RadioStation_RadioOptions#RadioHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations#CaptainStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation#CaptainLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Captain:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation#CaptainOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation_CaptainOptions#CaptainHuman" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station - leaves station interface empty for manual control"
toggle_mode = true
text = "Human"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation_CaptainOptions#CaptainEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation_CaptainOptions#CaptainNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_CaptainStation_CaptainOptions#CaptainHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations#EngineerStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation#EngineerLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Engineer:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation#EngineerOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation_EngineerOptions#EngineerHuman" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station - leaves station interface empty for manual control"
toggle_mode = true
text = "Human"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation_EngineerOptions#EngineerEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation_EngineerOptions#EngineerNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_EngineerStation_EngineerOptions#EngineerHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations#WeaponsStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation#WeaponsLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "First Mate (Weapons):"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation#WeaponsOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation_WeaponsOptions#WeaponsHuman" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
tooltip_text = "You will control this station - leaves station interface empty for manual control"
toggle_mode = true
text = "Human"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation_WeaponsOptions#WeaponsEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation_WeaponsOptions#WeaponsNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_PlayerSubPanel_PlayerStations_WeaponsStation_WeaponsOptions#WeaponsHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyRadioStation#EnemyRadioLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Radio Operator:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyRadioStation#EnemyRadioOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyRadioStation_EnemyRadioOptions#EnemyRadioEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyRadioStation_EnemyRadioOptions#EnemyRadioNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyRadioStation_EnemyRadioOptions#EnemyRadioHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations#EnemyCaptainStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyCaptainStation#EnemyCaptainLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Captain:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyCaptainStation#EnemyCaptainOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyCaptainStation_EnemyCaptainOptions#EnemyCaptainEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyCaptainStation_EnemyCaptainOptions#EnemyCaptainNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyCaptainStation_EnemyCaptainOptions#EnemyCaptainHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations#EnemyEngineerStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyEngineerStation#EnemyEngineerLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "Engineer:"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyEngineerStation#EnemyEngineerOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyEngineerStation_EnemyEngineerOptions#EnemyEngineerEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyEngineerStation_EnemyEngineerOptions#EnemyEngineerNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyEngineerStation_EnemyEngineerOptions#EnemyEngineerHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations#EnemyWeaponsStation" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyWeaponsStation#EnemyWeaponsLabel" type="Label" parent="."]
layout_mode = 2
size_flags_horizontal = 3
text = "First Mate (Weapons):"
vertical_alignment = 1

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyWeaponsStation#EnemyWeaponsOptions" type="HBoxContainer" parent="."]
layout_mode = 2
size_flags_horizontal = 3

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyWeaponsStation_EnemyWeaponsOptions#EnemyWeaponsEasy" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyWeaponsStation_EnemyWeaponsOptions#EnemyWeaponsNormal" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Normal AI"

[node name="ScrollContainer_VBoxContainer_EnemySubPanel_EnemyStations_EnemyWeaponsStation_EnemyWeaponsOptions#EnemyWeaponsHard" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard AI"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="ScrollContainer/VBoxContainer/StartGameButton" to="." method="_on_start_game_button_pressed"]
