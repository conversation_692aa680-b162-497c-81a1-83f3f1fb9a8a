[gd_scene load_steps=2 format=3 uid="uid://bq8xvn4j5k2ma"]

[ext_resource type="Script" path="res://PlayerSelection.gd" id="1_player_sel"]

[node name="PlayerSelection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_player_sel")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(600, 500)

[node name="TitleLabel" type="Label" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
text = "Captain Sonar - Player Selection"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="RoleSelection" type="VBoxContainer" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="RoleLabel" type="Label" parent="CenterContainer/VBoxContainer/RoleSelection"]
layout_mode = 2
text = "Choose Your Role:"
horizontal_alignment = 1

[node name="RoleDescription" type="Label" parent="CenterContainer/VBoxContainer/RoleSelection"]
layout_mode = 2
text = "Radio Operator: Track enemies | Captain: Navigate sub | Engineer: Repair systems | First Mate: Manage weapons"
horizontal_alignment = 1
autowrap_mode = 2

[node name="RoleButtons" type="HBoxContainer" parent="CenterContainer/VBoxContainer/RoleSelection"]
layout_mode = 2

[node name="RadioOperatorButton" type="Button" parent="CenterContainer/VBoxContainer/RoleSelection/RoleButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Radio Operator"

[node name="CaptainButton" type="Button" parent="CenterContainer/VBoxContainer/RoleSelection/RoleButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Captain"

[node name="EngineerButton" type="Button" parent="CenterContainer/VBoxContainer/RoleSelection/RoleButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Engineer"

[node name="FirstMateButton" type="Button" parent="CenterContainer/VBoxContainer/RoleSelection/RoleButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "First Mate"

[node name="SelectedRole" type="Label" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
text = "Selected Role: None"
horizontal_alignment = 1

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="DifficultySelection" type="VBoxContainer" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="DifficultyLabel" type="Label" parent="CenterContainer/VBoxContainer/DifficultySelection"]
layout_mode = 2
text = "AI Difficulty (for other 3 stations):"
horizontal_alignment = 1

[node name="DifficultyDescription" type="Label" parent="CenterContainer/VBoxContainer/DifficultySelection"]
layout_mode = 2
text = "Easy: Forgiving AI | Medium: Balanced challenge | Hard: Expert AI performance"
horizontal_alignment = 1
autowrap_mode = 2

[node name="DifficultyButtons" type="HBoxContainer" parent="CenterContainer/VBoxContainer/DifficultySelection"]
layout_mode = 2

[node name="EasyButton" type="Button" parent="CenterContainer/VBoxContainer/DifficultySelection/DifficultyButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy"

[node name="MediumButton" type="Button" parent="CenterContainer/VBoxContainer/DifficultySelection/DifficultyButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Medium"

[node name="HardButton" type="Button" parent="CenterContainer/VBoxContainer/DifficultySelection/DifficultyButtons"]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard"

[node name="SelectedDifficulty" type="Label" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
text = "AI Difficulty: None"
horizontal_alignment = 1

[node name="HSeparator3" type="HSeparator" parent="CenterContainer/VBoxContainer"]
layout_mode = 2

[node name="StartGameButton" type="Button" parent="CenterContainer/VBoxContainer"]
layout_mode = 2
disabled = true
text = "Start Game"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
[connection signal="pressed" from="CenterContainer/VBoxContainer/StartGameButton" to="." method="_on_start_game_button_pressed"]
