[gd_scene load_steps=2 format=3 uid="uid://b1hpdws5k06wh"]

[ext_resource type="Script" uid="uid://d0sys4x07ive3" path="res://PlayerSelection.gd" id="1_player_sel"]

[node name="PlayerSelection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_player_sel")

[node name="BackButton" type="Button" parent="."]
layout_mode = 0
offset_left = 20.0
offset_top = 20.0
offset_right = 100.0
offset_bottom = 50.0
text = "Back"

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 2
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CenterContainer_VBoxContainer#TitleLabel" type="Label" parent="."]
layout_mode = 2
text = "Captain Sonar - Player Selection"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CenterContainer_VBoxContainer#HSeparator" type="HSeparator" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer#RoleSelection" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer_RoleSelection#RoleLabel" type="Label" parent="."]
layout_mode = 2
text = "Choose Your Role:"
horizontal_alignment = 1

[node name="CenterContainer_VBoxContainer_RoleSelection#RoleDescription" type="Label" parent="."]
layout_mode = 2
text = "Radio Operator: Track enemies | Captain: Navigate sub | Engineer: Repair systems | First Mate: Manage weapons"
horizontal_alignment = 1
autowrap_mode = 2

[node name="CenterContainer_VBoxContainer_RoleSelection#RoleButtons" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer_RoleSelection_RoleButtons#RadioOperatorButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Radio Operator"

[node name="CenterContainer_VBoxContainer_RoleSelection_RoleButtons#CaptainButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Captain"

[node name="CenterContainer_VBoxContainer_RoleSelection_RoleButtons#EngineerButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Engineer"

[node name="CenterContainer_VBoxContainer_RoleSelection_RoleButtons#FirstMateButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "First Mate"

[node name="CenterContainer_VBoxContainer#SelectedRole" type="Label" parent="."]
layout_mode = 2
text = "Selected Role: None"
horizontal_alignment = 1

[node name="CenterContainer_VBoxContainer#HSeparator2" type="HSeparator" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer#DifficultySelection" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer_DifficultySelection#DifficultyLabel" type="Label" parent="."]
layout_mode = 2
text = "AI Difficulty (for other 3 stations):"
horizontal_alignment = 1

[node name="CenterContainer_VBoxContainer_DifficultySelection#DifficultyDescription" type="Label" parent="."]
layout_mode = 2
text = "Easy: Forgiving AI | Medium: Balanced challenge | Hard: Expert AI performance"
horizontal_alignment = 1
autowrap_mode = 2

[node name="CenterContainer_VBoxContainer_DifficultySelection#DifficultyButtons" type="HBoxContainer" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer_DifficultySelection_DifficultyButtons#EasyButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Easy"

[node name="CenterContainer_VBoxContainer_DifficultySelection_DifficultyButtons#MediumButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Medium"

[node name="CenterContainer_VBoxContainer_DifficultySelection_DifficultyButtons#HardButton" type="Button" parent="."]
layout_mode = 2
size_flags_horizontal = 3
toggle_mode = true
text = "Hard"

[node name="CenterContainer_VBoxContainer#SelectedDifficulty" type="Label" parent="."]
layout_mode = 2
text = "AI Difficulty: None"
horizontal_alignment = 1

[node name="CenterContainer_VBoxContainer#HSeparator3" type="HSeparator" parent="."]
layout_mode = 2

[node name="CenterContainer_VBoxContainer#StartGameButton" type="Button" parent="."]
layout_mode = 2
disabled = true
text = "Start Game"

[connection signal="pressed" from="BackButton" to="." method="_on_back_button_pressed"]
