# Player Selection System

## Overview
The Player Selection system allows players to choose their role on the submarine crew and set the AI difficulty for the other three stations. This creates a more immersive and customizable gameplay experience.

## Navigation Flow
```
Main Menu → Player Selection → Role-Specific Station
     ↑                              ↓
     ←──────── Back Button ──────────
```

## Player Roles

### 1. Radio Operator
- **Scene**: `radio_station.tscn`
- **Responsibilities**: 
  - Track enemy submarine movements
  - Manage communication systems
  - Plot enemy paths on the tactical map
  - Monitor radio frequencies

### 2. Captain
- **Scene**: `game.tscn` 
- **Responsibilities**:
  - Navigate the submarine on the tactical map
  - Make strategic movement decisions
  - View overall tactical situation
  - Command the crew

### 3. Engineer
- **Scene**: `engineer_station.tscn`
- **Responsibilities**:
  - Monitor submarine systems health
  - Repair damaged systems (weapons, sonar, engine, special)
  - Manage power and heat levels
  - Maintain submarine operational status

### 4. First Mate (Weapons Officer)
- **Scene**: `weapons_station.tscn`
- **Responsibilities**:
  - Manage weapons systems
  - Fire torpedoes and deploy mines
  - Launch drones and sonar pings
  - Charge weapon systems

## AI Difficulty Levels

The AI will control the other three stations not selected by the player:

### Easy
- Slower decision making
- Less optimal moves
- Simpler strategies
- More forgiving gameplay

### Medium
- Balanced AI performance
- Moderate strategic thinking
- Standard reaction times
- Recommended for most players

### Hard
- Advanced AI strategies
- Quick decision making
- Optimal play patterns
- Challenging gameplay

## User Interface

### Player Selection Screen
- **Role Selection**: Four toggle buttons for each crew role
- **Difficulty Selection**: Three toggle buttons for AI difficulty
- **Status Display**: Shows current selections
- **Start Game Button**: Enabled only when both role and difficulty are selected
- **Back Button**: Returns to main menu

### Station Interfaces
Each station includes:
- **Back Button**: Returns to player selection screen
- **Status Display**: Role-specific information
- **Control Panels**: Role-specific actions and controls
- **Real-time Updates**: Connected to GameState for live data

## Technical Implementation

### Files Created/Modified
1. **PlayerSelection.gd** - Main selection logic
2. **player_selection.tscn** - UI layout
3. **engineer_station.gd** - Engineer station logic
4. **weapons_station.gd** - Weapons station logic
5. **GameState.gd** - Added role and difficulty storage
6. **MainMenu.gd** - Updated navigation
7. **All station scenes** - Added back buttons

### Key Features
- **Toggle Button Groups**: Ensures only one role/difficulty selected
- **State Validation**: Start button disabled until selections made
- **GameState Integration**: Stores selections for other systems
- **Consistent Navigation**: All stations return to player selection
- **Role-Specific Content**: Each station tailored to its role

### Data Storage
Player selections are stored in GameState:
```gdscript
GameState.player_role: PlayerRole enum
GameState.ai_difficulty: AIDifficulty enum
```

## Usage Instructions

1. **Start Game**: Click "Start Game" from main menu
2. **Select Role**: Choose your preferred crew position
3. **Set Difficulty**: Choose AI difficulty for other stations
4. **Begin Mission**: Click "Start Game" to enter your station
5. **Navigation**: Use "Back" button to return to selection screen

## Future Enhancements
- Save player preferences
- Add role descriptions and tutorials
- Implement actual AI behavior based on difficulty
- Add multiplayer support for human crew members
- Create role-specific achievements and statistics
